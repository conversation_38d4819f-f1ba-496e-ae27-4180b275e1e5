"""
Тест парсинга Telegram ID
"""

def test_telegram_id_parsing():
    """Тест парсинга различных форматов Telegram ID"""
    
    test_cases = [
        "7265679697",
        " 7265679697 ",
        "7265679697\n",
        "7265679697\r\n",
        "955518340",
        "1023397024",
        "1268264380",
        "444555666",
        "abc123",  # Невалидный
        "123abc",  # Невалидный
        "",        # Пустой
    ]
    
    for test_case in test_cases:
        print(f"Тестируем: '{test_case}' (длина: {len(test_case)})")
        try:
            # Имитируем обработку как в боте
            telegram_id = int(test_case.strip())
            print(f"  ✅ Успешно: {telegram_id} (тип: {type(telegram_id)})")
        except ValueError as e:
            print(f"  ❌ Ошибка: {e}")
        print()

if __name__ == "__main__":
    test_telegram_id_parsing()
