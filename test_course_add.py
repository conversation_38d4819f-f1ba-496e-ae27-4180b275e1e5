"""
Тестовый скрипт для проверки добавления курса через SQLAlchemy
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import CourseRepository, SubjectRepository, init_database


async def test_add_course():
    """Тест добавления курса"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    print("📚 Добавление тестового курса...")
    try:
        # Добавляем курс
        course = await CourseRepository.create("Тестовый курс")
        print(f"✅ Курс '{course.name}' создан (ID: {course.id})")
        
        # Добавляем предметы к курсу
        subjects = ["Тестовый предмет 1", "Тестовый предмет 2"]
        for subject_name in subjects:
            subject = await SubjectRepository.create(subject_name, course.id)
            print(f"✅ Предмет '{subject.name}' создан для курса")
        
        print("\n📋 Проверяем созданные данные:")
        
        # Получаем все курсы
        all_courses = await CourseRepository.get_all()
        print(f"Всего курсов: {len(all_courses)}")
        for c in all_courses:
            print(f"  - {c.name} (ID: {c.id})")
        
        # Получаем предметы курса
        course_subjects = await SubjectRepository.get_by_course(course.id)
        print(f"Предметы курса '{course.name}': {len(course_subjects)}")
        for s in course_subjects:
            print(f"  - {s.name} (ID: {s.id})")
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")


if __name__ == "__main__":
    asyncio.run(test_add_course())
