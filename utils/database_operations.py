"""
Функции для работы с базой данных через SQLAlchemy
"""
from typing import Optional, List
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession
from database import (
    User, Course, Subject, Group, BonusTask, Microtopic, MonthTest,
    get_db_session
)


# Функции для работы с пользователями
async def get_user_by_telegram_id(telegram_id: int) -> Optional[User]:
    """Получить пользователя по Telegram ID"""
    async with get_db_session() as session:
        result = await session.execute(
            select(User).where(User.telegram_id == telegram_id)
        )
        return result.scalar_one_or_none()


async def create_user(telegram_id: int, name: str, role: str = 'student') -> User:
    """Создать нового пользователя"""
    async with get_db_session() as session:
        user = User(telegram_id=telegram_id, name=name, role=role)
        session.add(user)
        await session.commit()
        await session.refresh(user)
        return user


async def get_user_role(telegram_id: int) -> str:
    """Получить роль пользователя"""
    user = await get_user_by_telegram_id(telegram_id)
    return user.role if user else 'student'


# Функции для работы с курсами
async def get_all_courses() -> List[Course]:
    """Получить все курсы"""
    async with get_db_session() as session:
        result = await session.execute(select(Course).order_by(Course.name))
        return list(result.scalars().all())


async def add_course(name: str, description: str = None) -> Course:
    """Добавить новый курс"""
    async with get_db_session() as session:
        course = Course(name=name, description=description)
        session.add(course)
        await session.commit()
        await session.refresh(course)
        return course


async def remove_course(course_id: int) -> bool:
    """Удалить курс"""
    async with get_db_session() as session:
        result = await session.execute(
            delete(Course).where(Course.id == course_id)
        )
        await session.commit()
        return result.rowcount > 0


# Функции для работы с предметами
async def get_subjects_by_course(course_id: int) -> List[Subject]:
    """Получить предметы по курсу"""
    async with get_db_session() as session:
        result = await session.execute(
            select(Subject)
            .where(Subject.course_id == course_id)
            .order_by(Subject.name)
        )
        return list(result.scalars().all())


async def get_all_subjects() -> List[Subject]:
    """Получить все предметы"""
    async with get_db_session() as session:
        result = await session.execute(select(Subject).order_by(Subject.name))
        return list(result.scalars().all())


async def add_subject(name: str, course_id: int = None) -> Subject:
    """Добавить новый предмет"""
    async with get_db_session() as session:
        subject = Subject(name=name, course_id=course_id)
        session.add(subject)
        await session.commit()
        await session.refresh(subject)
        return subject


async def remove_subject(subject_id: int) -> bool:
    """Удалить предмет"""
    async with get_db_session() as session:
        result = await session.execute(
            delete(Subject).where(Subject.id == subject_id)
        )
        await session.commit()
        return result.rowcount > 0


# Функции для работы с группами
async def get_groups_by_subject(subject_id: int) -> List[Group]:
    """Получить группы по предмету"""
    async with get_db_session() as session:
        result = await session.execute(
            select(Group)
            .where(Group.subject_id == subject_id)
            .order_by(Group.name)
        )
        return list(result.scalars().all())


async def add_group(name: str, subject_id: int) -> Group:
    """Добавить новую группу"""
    async with get_db_session() as session:
        group = Group(name=name, subject_id=subject_id)
        session.add(group)
        await session.commit()
        await session.refresh(group)
        return group


async def remove_group(group_id: int) -> bool:
    """Удалить группу"""
    async with get_db_session() as session:
        result = await session.execute(
            delete(Group).where(Group.id == group_id)
        )
        await session.commit()
        return result.rowcount > 0


# Функции для работы с бонусными заданиями
async def get_all_bonus_tasks() -> List[BonusTask]:
    """Получить все бонусные задания"""
    async with get_db_session() as session:
        result = await session.execute(
            select(BonusTask).order_by(BonusTask.created_at.desc())
        )
        return list(result.scalars().all())


async def add_bonus_task(title: str, description: str, points: int = 0) -> BonusTask:
    """Добавить новое бонусное задание"""
    async with get_db_session() as session:
        task = BonusTask(title=title, description=description, points=points)
        session.add(task)
        await session.commit()
        await session.refresh(task)
        return task


async def remove_bonus_task(task_id: int) -> bool:
    """Удалить бонусное задание"""
    async with get_db_session() as session:
        result = await session.execute(
            delete(BonusTask).where(BonusTask.id == task_id)
        )
        await session.commit()
        return result.rowcount > 0


# Функции для работы с микротемами
async def get_microtopics_by_subject(subject_id: int) -> List[Microtopic]:
    """Получить микротемы по предмету"""
    async with get_db_session() as session:
        result = await session.execute(
            select(Microtopic)
            .where(Microtopic.subject_id == subject_id)
            .order_by(Microtopic.number)
        )
        return list(result.scalars().all())


async def add_microtopic(number: int, name: str, subject_id: int) -> Microtopic:
    """Добавить новую микротему"""
    async with get_db_session() as session:
        microtopic = Microtopic(number=number, name=name, subject_id=subject_id)
        session.add(microtopic)
        await session.commit()
        await session.refresh(microtopic)
        return microtopic


# Функции для работы с месячными тестами
async def get_month_tests_by_subject(subject_id: int) -> List[MonthTest]:
    """Получить месячные тесты по предмету"""
    async with get_db_session() as session:
        result = await session.execute(
            select(MonthTest)
            .where(MonthTest.subject_id == subject_id)
            .order_by(MonthTest.created_at.desc())
        )
        return list(result.scalars().all())


async def add_month_test(name: str, course_id: int, subject_id: int, microtopic_numbers: List[int]) -> MonthTest:
    """Добавить новый месячный тест с привязкой к микротемам"""
    async with get_db_session() as session:
        # Создаем тест
        month_test = MonthTest(name=name, course_id=course_id, subject_id=subject_id)
        session.add(month_test)
        await session.flush()  # Получаем ID теста
        
        # Получаем микротемы по номерам
        microtopics_result = await session.execute(
            select(Microtopic)
            .where(Microtopic.subject_id == subject_id)
            .where(Microtopic.number.in_(microtopic_numbers))
        )
        microtopics = list(microtopics_result.scalars().all())
        
        # Привязываем микротемы к тесту
        month_test.microtopics = microtopics
        
        await session.commit()
        await session.refresh(month_test)
        return month_test


async def remove_month_test(test_id: int) -> bool:
    """Удалить месячный тест"""
    async with get_db_session() as session:
        result = await session.execute(
            delete(MonthTest).where(MonthTest.id == test_id)
        )
        await session.commit()
        return result.rowcount > 0
