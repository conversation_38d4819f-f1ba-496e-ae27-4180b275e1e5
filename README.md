# 🤖 Телеграм Бот для Образовательной Платформы

Многофункциональный телеграм бот для управления образовательным процессом с системой ролей, тестированием и магазином бонусов.

## 🚀 Быстрый старт

### Деплой на сервер
```bash
# Клонируйте репозиторий
git clone <your-repo-url> telebot
cd telebot

# Запустите автоматический деплой
sudo ./deploy.sh
```

### Локальная разработка
```bash
# Создайте виртуальное окружение
python -m venv venv
source venv/bin/activate  # Linux/Mac
# или
venv\Scripts\activate     # Windows

# Установите зависимости
pip install -r requirements.txt

# Настройте переменные окружения
cp .env.example .env
# Отредактируйте .env файл

# Запустите бота
python main.py
```

## 🏗️ Архитектура

### Структура проекта
```
telebot/
├── main.py                 # Точка входа
├── handlers/              # Обработчики команд
│   ├── admin/            # Админ панель
│   ├── student/          # Функции студентов
│   ├── teacher/          # Функции преподавателей
│   └── common/           # Общие команды
├── database/             # Работа с БД
│   ├── models.py         # Модели SQLAlchemy
│   ├── database.py       # Подключение к БД
│   └── repositories/     # Репозитории для операций
├── middleware/           # Промежуточное ПО
├── utils/               # Утилиты
├── tests/               # Тесты
└── scripts/             # Скрипты деплоя
```

### Роли пользователей
- **👑 Админ** - полный доступ к системе
- **👨‍💼 Менеджер** - управление курсами и тестами
- **👨‍🏫 Преподаватель** - создание контента
- **👨‍🎓 Куратор** - работа с группами
- **🎓 Студент** - прохождение обучения

## 🎯 Основные функции

### Для студентов
- 📚 Прохождение тестов по предметам
- 🏆 Система достижений и рейтингов
- 🛒 Магазин бонусов
- 📊 Просмотр статистики

### Для преподавателей
- ✏️ Создание и редактирование тестов
- 📈 Просмотр результатов студентов
- 📝 Управление вопросами

### Для менеджеров
- 🎛️ Управление курсами и предметами
- 👥 Управление пользователями
- 📊 Аналитика и отчеты

### Для админов
- 🔧 Полное управление системой
- 👤 Управление ролями
- ⚙️ Системные настройки

## 🛠️ Технологии

- **Python 3.11+** - основной язык
- **aiogram 3.x** - фреймворк для Telegram Bot API
- **SQLAlchemy** - ORM для работы с БД
- **PostgreSQL** - база данных
- **Docker** - контейнеризация
- **Nginx** - веб-сервер и прокси

## 📦 Деплой

### Требования
- Ubuntu/Debian/Kali Linux
- Docker и Docker Compose
- Домен с SSL сертификатом (для webhook)

### Автоматический деплой
Скрипт `deploy.sh` автоматически:
- Устанавливает Docker и зависимости
- Настраивает переменные окружения
- Создает SSL сертификаты
- Запускает все сервисы

### Переменные окружения
```bash
BOT_TOKEN=your_bot_token
POSTGRES_PASSWORD=secure_password
WEBHOOK_HOST=https://yourdomain.com
WEBHOOK_MODE=true
```

## 🧪 Тестирование

```bash
# Запуск тестов
python -m pytest tests/

# Запуск с покрытием
python -m pytest tests/ --cov=.
```

## 📊 Мониторинг

```bash
# Просмотр логов
docker-compose logs -f bot

# Статус контейнеров
docker-compose ps

# Мониторинг ресурсов
docker stats
```

## 🤝 Разработка

### Добавление новых команд
1. Создайте обработчик в `handlers/`
2. Зарегистрируйте в `main.py`
3. Добавьте тесты в `tests/`

### Работа с базой данных
1. Создайте модель в `database/models.py`
2. Создайте репозиторий в `database/repositories/`
3. Выполните миграцию

## 📞 Поддержка

- 📧 Email: <EMAIL>
- 💬 Telegram: @support_bot
- 🐛 Issues: GitHub Issues

## 📄 Лицензия

MIT License - см. файл LICENSE
