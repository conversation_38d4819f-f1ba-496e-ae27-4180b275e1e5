"""
Тест добавления предмета
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import SubjectRepository, init_database


async def test_subject_add():
    """Тест добавления предмета"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    print("📖 Текущие предметы:")
    subjects = await SubjectRepository.get_all()
    for subject in subjects:
        print(f"  - {subject.name} (ID: {subject.id})")
    
    print(f"\n📚 Добавление нового предмета...")
    try:
        new_subject = await SubjectRepository.create("Тестовый предмет")
        print(f"✅ Предмет '{new_subject.name}' создан (ID: {new_subject.id})")
    except Exception as e:
        print(f"❌ Ошибка при создании предмета: {e}")
    
    print(f"\n📖 Предметы после добавления:")
    subjects = await SubjectRepository.get_all()
    for subject in subjects:
        print(f"  - {subject.name} (ID: {subject.id})")


if __name__ == "__main__":
    asyncio.run(test_subject_add())
