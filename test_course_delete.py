"""
Тест удаления курса с Many-to-Many связями
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import CourseRepository, SubjectRepository, init_database


async def test_course_delete():
    """Тест удаления курса"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    print("📚 Создание тестового курса...")
    # Создаем курс
    course = await CourseRepository.create("Тестовый курс для удаления")
    print(f"✅ Курс '{course.name}' создан (ID: {course.id})")
    
    # Получаем существующие предметы
    subjects = await SubjectRepository.get_all()
    print(f"📖 Найдено предметов: {len(subjects)}")
    
    # Привязываем несколько предметов к курсу
    if len(subjects) >= 3:
        for i in range(3):
            success = await SubjectRepository.add_to_course(subjects[i].id, course.id)
            if success:
                print(f"✅ Предмет '{subjects[i].name}' привязан к курсу")
    
    print(f"\n📋 Проверяем связи курса {course.id}:")
    course_subjects = await SubjectRepository.get_by_course(course.id)
    print(f"Предметов в курсе: {len(course_subjects)}")
    for subject in course_subjects:
        print(f"  - {subject.name}")
    
    print(f"\n🗑 Удаляем курс {course.id}...")
    try:
        success = await CourseRepository.delete(course.id)
        if success:
            print("✅ Курс успешно удален!")
        else:
            print("❌ Курс не был удален")
    except Exception as e:
        print(f"❌ Ошибка при удалении курса: {e}")
    
    print("\n📋 Проверяем, что курс удален:")
    all_courses = await CourseRepository.get_all()
    deleted_course = next((c for c in all_courses if c.id == course.id), None)
    if deleted_course:
        print(f"❌ Курс все еще существует: {deleted_course.name}")
    else:
        print("✅ Курс успешно удален из базы")


if __name__ == "__main__":
    asyncio.run(test_course_delete())
