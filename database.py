from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from os import getenv
from dotenv import load_dotenv

load_dotenv()

# Формируем DATABASE_URL из отдельных переменных для избежания дублирования
POSTGRES_USER = getenv("POSTGRES_USER", "telebot_user")
POSTGRES_PASSWORD = getenv("POSTGRES_PASSWORD", "your_secure_password")
POSTGRES_DB = getenv("POSTGRES_DB", "telebot")
POSTGRES_HOST = getenv("POSTGRES_HOST", "postgres")
POSTGRES_PORT = getenv("POSTGRES_PORT", "5432")

DATABASE_URL = f"postgresql+asyncpg://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

# Базовый класс для моделей
class Base(DeclarativeBase):
    pass

# Таблица связи многие-ко-многим для тестов и микротем
month_tests_microtopics = Table(
    'month_tests_microtopics',
    Base.metadata,
    Column('month_test_id', Integer, ForeignKey('month_tests.id'), primary_key=True),
    Column('microtopic_id', Integer, ForeignKey('microtopics.id'), primary_key=True)
)

# Модели таблиц
class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    telegram_id = Column(Integer, unique=True, nullable=False)
    name = Column(String(255), nullable=False)
    role = Column(String(50), default='student')
    created_at = Column(DateTime, server_default=func.now())

class Course(Base):
    __tablename__ = 'courses'

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    created_at = Column(DateTime, server_default=func.now())

    subjects = relationship("Subject", back_populates="course")

class Subject(Base):
    __tablename__ = 'subjects'

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    course_id = Column(Integer, ForeignKey('courses.id'))
    created_at = Column(DateTime, server_default=func.now())

    course = relationship("Course", back_populates="subjects")
    groups = relationship("Group", back_populates="subject")
    month_tests = relationship("MonthTest", back_populates="subject")

class Group(Base):
    __tablename__ = 'groups'

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    subject_id = Column(Integer, ForeignKey('subjects.id'))
    created_at = Column(DateTime, server_default=func.now())

    subject = relationship("Subject", back_populates="groups")

class BonusTask(Base):
    __tablename__ = 'bonus_tasks'

    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    points = Column(Integer, default=0)
    created_at = Column(DateTime, server_default=func.now())

class Microtopic(Base):
    __tablename__ = 'microtopics'

    id = Column(Integer, primary_key=True)
    number = Column(Integer, nullable=False)
    name = Column(String(255))
    subject_id = Column(Integer, ForeignKey('subjects.id'))
    created_at = Column(DateTime, server_default=func.now())

class MonthTest(Base):
    __tablename__ = 'month_tests'

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    course_id = Column(Integer, ForeignKey('courses.id'))
    subject_id = Column(Integer, ForeignKey('subjects.id'))
    created_at = Column(DateTime, server_default=func.now())

    course = relationship("Course")
    subject = relationship("Subject", back_populates="month_tests")
    microtopics = relationship("Microtopic", secondary=month_tests_microtopics)

# Создание движка и сессии
engine = create_async_engine(DATABASE_URL, echo=False)
async_session = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

# Функции для работы с пользователями
async def get_user_by_telegram_id(telegram_id: int) -> Optional[Dict]:
    """Получить пользователя по Telegram ID"""
    return await db.fetchrow(
        "SELECT * FROM users WHERE telegram_id = $1", 
        telegram_id
    )

async def create_user(telegram_id: int, name: str, role: str = 'student') -> Dict:
    """Создать нового пользователя"""
    return await db.fetchrow(
        """INSERT INTO users (telegram_id, name, role) 
           VALUES ($1, $2, $3) 
           RETURNING *""",
        telegram_id, name, role
    )

async def get_user_role(telegram_id: int) -> str:
    """Получить роль пользователя"""
    role = await db.fetchval(
        "SELECT role FROM users WHERE telegram_id = $1", 
        telegram_id
    )
    return role or 'student'

# Функции для работы с курсами
async def get_all_courses() -> List[Dict]:
    """Получить все курсы"""
    return await db.fetch("SELECT * FROM courses ORDER BY name")

async def add_course(name: str, description: str = None) -> Dict:
    """Добавить новый курс"""
    return await db.fetchrow(
        """INSERT INTO courses (name, description) 
           VALUES ($1, $2) 
           RETURNING *""",
        name, description
    )

async def remove_course(course_id: int) -> bool:
    """Удалить курс"""
    result = await db.execute("DELETE FROM courses WHERE id = $1", course_id)
    return result == "DELETE 1"

# Функции для работы с предметами
async def get_subjects_by_course(course_id: int) -> List[Dict]:
    """Получить предметы по курсу"""
    return await db.fetch(
        "SELECT * FROM subjects WHERE course_id = $1 ORDER BY name", 
        course_id
    )

async def get_all_subjects() -> List[Dict]:
    """Получить все предметы"""
    return await db.fetch("SELECT * FROM subjects ORDER BY name")

async def add_subject(name: str, course_id: int = None) -> Dict:
    """Добавить новый предмет"""
    return await db.fetchrow(
        """INSERT INTO subjects (name, course_id) 
           VALUES ($1, $2) 
           RETURNING *""",
        name, course_id
    )

async def remove_subject(subject_id: int) -> bool:
    """Удалить предмет"""
    result = await db.execute("DELETE FROM subjects WHERE id = $1", subject_id)
    return result == "DELETE 1"

# Функции для работы с группами
async def get_groups_by_subject(subject_id: int) -> List[Dict]:
    """Получить группы по предмету"""
    return await db.fetch(
        "SELECT * FROM groups WHERE subject_id = $1 ORDER BY name", 
        subject_id
    )

async def add_group(name: str, subject_id: int) -> Dict:
    """Добавить новую группу"""
    return await db.fetchrow(
        """INSERT INTO groups (name, subject_id) 
           VALUES ($1, $2) 
           RETURNING *""",
        name, subject_id
    )

async def remove_group(group_id: int) -> bool:
    """Удалить группу"""
    result = await db.execute("DELETE FROM groups WHERE id = $1", group_id)
    return result == "DELETE 1"

# Функции для работы с бонусными заданиями
async def get_all_bonus_tasks() -> List[Dict]:
    """Получить все бонусные задания"""
    return await db.fetch("SELECT * FROM bonus_tasks ORDER BY created_at DESC")

async def add_bonus_task(title: str, description: str, points: int = 0) -> Dict:
    """Добавить новое бонусное задание"""
    return await db.fetchrow(
        """INSERT INTO bonus_tasks (title, description, points) 
           VALUES ($1, $2, $3) 
           RETURNING *""",
        title, description, points
    )

async def remove_bonus_task(task_id: int) -> bool:
    """Удалить бонусное задание"""
    result = await db.execute("DELETE FROM bonus_tasks WHERE id = $1", task_id)
    return result == "DELETE 1"

# Функция инициализации базы данных
async def init_database():
    """Инициализация подключения к базе данных"""
    await db.connect()

# Функция закрытия соединения
async def close_database():
    """Закрытие соединения с базой данных"""
    await db.disconnect()
