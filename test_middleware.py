"""
Тест middleware для проверки работы с базой данных
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import UserRepository, init_database


async def test_middleware_logic():
    """Тест логики middleware"""
    print("🚀 Инициализация базы данных...")
    await init_database()
    
    # Тестируем разные сценарии
    test_cases = [
        (955518340, "Андрей Климов"),      # Существующий админ
        (7265679697, "Медина Махамбет"),   # Существующий менеджер
        (999999999, "Новый Пользователь"), # Несуществующий пользователь
    ]
    
    for user_id, user_name in test_cases:
        print(f"\n👤 Тестируем пользователя: {user_name} (ID: {user_id})")
        
        try:
            # Имитируем логику middleware
            user = await UserRepository.get_by_telegram_id(user_id)
            
            if user:
                # Пользователь найден в базе
                role = user.role
                print(f"✅ Роль из БД: {role}")
            else:
                # Пользователь не найден - создаем нового
                print(f"⚠️ Пользователь не найден в БД, создаем нового")
                
                # Проверяем, является ли пользователь админом по ID
                admin_ids = [955518340]  # Андрей Климов
                
                if user_id in admin_ids:
                    role = "admin"
                    print(f"🔑 Создан админ: {user_name}")
                else:
                    role = "student"
                    print(f"👤 Создан студент: {user_name}")
                
                # Создаем пользователя в базе
                try:
                    new_user = await UserRepository.create(user_id, user_name, role)
                    print(f"✅ Пользователь создан в БД: {new_user.name} ({new_user.role})")
                except Exception as create_error:
                    print(f"❌ Ошибка создания пользователя: {create_error}")
                    
        except Exception as e:
            print(f"❌ Ошибка работы с БД: {e}")
            # Fallback: определяем роль по ID
            admin_ids = [955518340]  # Андрей Климов
            role = "admin" if user_id in admin_ids else "student"
            print(f"🔄 Fallback роль: {role}")
    
    print("\n📋 Итоговые пользователи в базе:")
    all_users = await UserRepository.get_all()
    for user in all_users:
        print(f"  - {user.name} (ID: {user.telegram_id}, роль: {user.role})")


if __name__ == "__main__":
    asyncio.run(test_middleware_logic())
